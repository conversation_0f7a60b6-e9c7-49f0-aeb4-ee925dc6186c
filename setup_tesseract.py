#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载并设置Tesseract OCR（便携版）
"""

import os
import urllib.request
import zipfile
import sys
from pathlib import Path

def download_tesseract():
    """下载Tesseract便携版"""
    tesseract_dir = Path("tesseract")
    
    if tesseract_dir.exists():
        print("✅ Tesseract已存在")
        return tesseract_dir
    
    print("📥 正在下载Tesseract OCR...")
    
    # 使用GitHub上的便携版Tesseract
    url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
    
    try:
        # 创建tesseract目录
        tesseract_dir.mkdir(exist_ok=True)
        
        print("⚠️  需要手动安装Tesseract OCR")
        print(f"请访问: {url}")
        print("或者使用以下简化方案...")
        
        return None
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def create_simple_version():
    """创建不需要OCR的简化版本"""
    print("🔄 创建简化版本（无需OCR）...")
    
    # 修改原始脚本，移除OCR依赖
    simple_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片转ANKI工具 - 手动添加内容
"""

import os
import csv
import shutil
from pathlib import Path

def create_anki_cards():
    """创建ANKI卡片"""
    
    # 创建媒体文件夹
    media_folder = "anki_media"
    os.makedirs(media_folder, exist_ok=True)
    
    # 复制前10张图片
    copied_files = []
    for i in range(1, 11):
        filename = f"初中语文九年级上册_页面_{i:03d}.jpg"
        if os.path.exists(filename):
            shutil.copy2(filename, os.path.join(media_folder, filename))
            copied_files.append(filename)
            print(f"✅ 已复制: {filename}")
    
    # 创建卡片模板
    cards = []
    for i, image_file in enumerate(copied_files, 1):
        # 基础填空题模板
        card_content = f\'\'\'<img src="{image_file}"><br><br>
根据图片内容，这是第{{{{c1::{i}}}}}页的内容。<br>
主要讲述了{{{{c2::请根据图片内容填写}}}}的知识点。\'\'\'
        
        cards.append([card_content, f"初中语文::九年级上册::页面{i}"])
    
    # 导出ANKI文件
    output_file = "anki_cards.txt"
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter='\\t')
        for card in cards:
            writer.writerow(card)
    
    print(f"\\n✅ 已创建 {len(cards)} 张卡片")
    print(f"📁 输出文件: {output_file}")
    print(f"🖼️  图片文件夹: {media_folder}")
    
    # 创建手动编辑模板
    create_manual_template(copied_files)
    
    print("\\n📖 使用说明:")
    print("1. 编辑 manual_template.txt 文件，添加具体的问题和答案")
    print("2. 运行 python process_manual.py 生成最终卡片")
    print("3. 在ANKI中导入生成的文件")

def create_manual_template(image_files):
    """创建手动编辑模板"""
    with open("manual_template.txt", 'w', encoding='utf-8') as f:
        f.write("# 手动编辑ANKI卡片模板\\n")
        f.write("# 格式: 图片文件名|问题|答案|标签\\n\\n")
        
        for i, image_file in enumerate(image_files, 1):
            f.write(f"{image_file}|这是关于什么的内容？|第{i}页的语文知识|初中语文,九年级上册\\n")
    
    print("📝 已创建手动编辑模板: manual_template.txt")

if __name__ == "__main__":
    create_anki_cards()
'''
    
    with open("simple_anki_tool.py", 'w', encoding='utf-8') as f:
        f.write(simple_script)
    
    # 创建处理手动模板的脚本
    process_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def process_manual_template():
    """处理手动编辑的模板"""
    cards = []
    
    try:
        with open("manual_template.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            if line.startswith('#') or not line.strip():
                continue
            
            parts = line.strip().split('|')
            if len(parts) >= 3:
                image_file, question, answer = parts[0], parts[1], parts[2]
                tags = parts[3] if len(parts) > 3 else "初中语文"
                
                # 创建填空题
                cloze_question = question.replace(answer, f"{{{{c1::{answer}}}}}")
                card_content = f'<img src="{image_file}"><br><br>{cloze_question}'
                
                cards.append([card_content, tags.replace(',', '::')])
        
        # 导出最终卡片
        with open("final_anki_cards.txt", 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\\t')
            for card in cards:
                writer.writerow(card)
        
        print(f"✅ 已生成 {len(cards)} 张最终卡片: final_anki_cards.txt")
        
    except FileNotFoundError:
        print("❌ 找不到 manual_template.txt 文件")
    except Exception as e:
        print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    process_manual_template()
'''
    
    with open("process_manual.py", 'w', encoding='utf-8') as f:
        f.write(process_script)
    
    print("✅ 已创建简化版工具:")
    print("  - simple_anki_tool.py (主工具)")
    print("  - process_manual.py (处理手动模板)")

def main():
    print("🚀 设置ANKI转换工具")
    print("=" * 40)
    
    # 尝试下载Tesseract
    tesseract_path = download_tesseract()
    
    if not tesseract_path:
        print("\\n🔄 使用简化方案...")
        create_simple_version()
        
        print("\\n📋 下一步:")
        print("1. 运行: python simple_anki_tool.py")
        print("2. 编辑生成的 manual_template.txt 文件")
        print("3. 运行: python process_manual.py")
    
    print("\\n🗑️  清理说明:")
    print("用完后删除以下文件夹即可完全清除:")
    print("  - anki_env/ (虚拟环境)")
    print("  - anki_media/ (图片文件)")
    print("  - tesseract/ (如果下载了)")

if __name__ == "__main__":
    main()
