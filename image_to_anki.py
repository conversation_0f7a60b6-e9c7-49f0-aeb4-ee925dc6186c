#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将图片文件转换为ANKI填空题卡片格式
"""

import os
import re
import csv
import base64
from PIL import Image
import pytesseract
from pathlib import Path

class ImageToAnkiConverter:
    def __init__(self, image_folder="."):
        self.image_folder = Path(image_folder)
        self.output_file = "anki_cards.txt"
        self.media_folder = "anki_media"
        
        # 创建媒体文件夹
        os.makedirs(self.media_folder, exist_ok=True)
        
    def extract_text_from_image(self, image_path):
        """使用OCR从图片中提取文字"""
        try:
            # 打开图片
            image = Image.open(image_path)
            
            # 使用pytesseract进行OCR识别，指定中文语言
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            
            # 清理文本
            text = text.strip()
            text = re.sub(r'\n+', '\n', text)  # 合并多个换行符
            text = re.sub(r'\s+', ' ', text)   # 合并多个空格
            
            return text
        except Exception as e:
            print(f"处理图片 {image_path} 时出错: {e}")
            return ""
    
    def create_cloze_cards(self, text, image_filename):
        """从文本创建填空题卡片"""
        cards = []
        
        if not text.strip():
            return cards
        
        # 将文本分割成句子
        sentences = re.split(r'[。！？；]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        for i, sentence in enumerate(sentences):
            if len(sentence) < 5:  # 跳过太短的句子
                continue
                
            # 寻找关键词进行填空
            words = sentence.split()
            if len(words) < 2:
                continue
            
            # 选择中间的词作为填空对象
            for j, word in enumerate(words):
                if len(word) >= 2 and not re.match(r'^[0-9\s\.,，。！？；：""''（）]+$', word):
                    # 创建填空题
                    cloze_sentence = sentence.replace(word, f"{{{{c1::{word}}}}}", 1)
                    
                    # 添加图片引用
                    card_content = f'<img src="{image_filename}"><br><br>{cloze_sentence}'
                    
                    cards.append({
                        'content': card_content,
                        'source': image_filename,
                        'original_text': sentence
                    })
                    break  # 每个句子只创建一个填空题
        
        return cards
    
    def copy_image_to_media(self, image_path):
        """复制图片到媒体文件夹"""
        try:
            image_filename = os.path.basename(image_path)
            media_path = os.path.join(self.media_folder, image_filename)
            
            # 复制图片文件
            image = Image.open(image_path)
            image.save(media_path, quality=85, optimize=True)
            
            return image_filename
        except Exception as e:
            print(f"复制图片 {image_path} 时出错: {e}")
            return None
    
    def process_images(self, num_images=10):
        """处理指定数量的图片"""
        # 获取前num_images张图片
        image_files = []
        for i in range(1, num_images + 1):
            filename = f"初中语文九年级上册_页面_{i:03d}.jpg"
            image_path = self.image_folder / filename
            if image_path.exists():
                image_files.append(image_path)
        
        all_cards = []
        
        print(f"开始处理 {len(image_files)} 张图片...")
        
        for i, image_path in enumerate(image_files, 1):
            print(f"处理第 {i} 张图片: {image_path.name}")
            
            # 提取文字
            text = self.extract_text_from_image(image_path)
            
            if text:
                print(f"  提取到文字: {text[:50]}...")
                
                # 复制图片到媒体文件夹
                image_filename = self.copy_image_to_media(image_path)
                
                if image_filename:
                    # 创建填空题卡片
                    cards = self.create_cloze_cards(text, image_filename)
                    all_cards.extend(cards)
                    print(f"  创建了 {len(cards)} 张卡片")
            else:
                print(f"  未能提取到文字")
        
        return all_cards
    
    def export_to_anki(self, cards):
        """导出为ANKI可导入的格式"""
        if not cards:
            print("没有卡片可导出")
            return
        
        # 创建ANKI导入文件
        with open(self.output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\t')
            
            # 写入卡片数据
            for card in cards:
                # ANKI填空题格式: 内容, 标签
                writer.writerow([
                    card['content'],
                    '初中语文::九年级上册'
                ])
        
        print(f"已导出 {len(cards)} 张卡片到 {self.output_file}")
        print(f"图片文件已复制到 {self.media_folder} 文件夹")
        print("\n导入说明:")
        print("1. 在ANKI中选择 '文件' -> '导入'")
        print(f"2. 选择 {self.output_file} 文件")
        print("3. 确保字段类型设置为 'Cloze'")
        print(f"4. 将 {self.media_folder} 文件夹中的图片复制到ANKI的媒体文件夹")

def main():
    # 检查是否安装了必要的依赖
    try:
        import pytesseract
        from PIL import Image
    except ImportError as e:
        print("缺少必要的依赖包，请安装:")
        print("pip install pytesseract pillow")
        print("另外需要安装Tesseract OCR软件")
        return
    
    # 创建转换器实例
    converter = ImageToAnkiConverter()
    
    # 处理前10张图片
    cards = converter.process_images(10)
    
    # 导出为ANKI格式
    converter.export_to_anki(cards)

if __name__ == "__main__":
    main()
