#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理手动编辑的模板，生成最终的ANKI卡片
"""

import csv
import re

def parse_manual_template(template_file="manual_cards_template.txt"):
    """解析手动编辑的模板文件"""
    cards = []
    
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按分隔线分割卡片
        card_sections = content.split('-' * 50)
        
        for section in card_sections:
            if not section.strip() or section.startswith('#'):
                continue
            
            # 提取信息
            image_match = re.search(r'图片:\s*(.+)', section)
            question_match = re.search(r'问题:\s*(.+)', section)
            answer_match = re.search(r'答案:\s*(.+)', section)
            tags_match = re.search(r'标签:\s*(.+)', section)
            
            if all([image_match, question_match, answer_match]):
                image = image_match.group(1).strip()
                question = question_match.group(1).strip()
                answer = answer_match.group(1).strip()
                tags = tags_match.group(1).strip() if tags_match else "初中语文"
                
                # 跳过模板默认内容
                if "请在这里写入" in question or "请在这里写入" in answer:
                    continue
                
                # 创建填空题格式
                cloze_question = question.replace(answer, f"{{{{c1::{answer}}}}}")
                
                # 生成卡片内容
                card_content = f'<img src="{image}"><br><br>{cloze_question}'
                
                cards.append({
                    'content': card_content,
                    'tags': tags.replace(',', '::')
                })
    
    except FileNotFoundError:
        print(f"❌ 找不到模板文件: {template_file}")
        return []
    except Exception as e:
        print(f"❌ 解析模板文件时出错: {e}")
        return []
    
    return cards

def export_final_cards(cards, output_file="final_anki_cards.txt"):
    """导出最终的ANKI卡片"""
    if not cards:
        print("❌ 没有有效的卡片数据")
        return
    
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter='\t')
        
        for card in cards:
            writer.writerow([
                card['content'],
                card['tags']
            ])
    
    print(f"✅ 已生成 {len(cards)} 张最终卡片: {output_file}")

def main():
    print("🔄 处理手动编辑的模板...")
    
    # 解析模板
    cards = parse_manual_template()
    
    if cards:
        # 导出最终卡片
        export_final_cards(cards)
        
        print("\n📖 导入ANKI说明:")
        print("1. 在ANKI中选择 '文件' -> '导入'")
        print("2. 选择 final_anki_cards.txt 文件")
        print("3. 字段类型选择 'Cloze'")
        print("4. 确保anki_media文件夹中的图片已复制到ANKI媒体文件夹")
    else:
        print("❌ 没有找到有效的卡片内容")
        print("请确保已经编辑了 manual_cards_template.txt 文件")

if __name__ == "__main__":
    main()
