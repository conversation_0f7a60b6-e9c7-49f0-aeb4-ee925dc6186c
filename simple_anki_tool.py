#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片转ANKI工具 - 手动添加内容
"""

import os
import csv
import shutil
from pathlib import Path

def create_anki_cards():
    """创建ANKI卡片"""
    
    # 创建媒体文件夹
    media_folder = "anki_media"
    os.makedirs(media_folder, exist_ok=True)
    
    # 复制前10张图片
    copied_files = []
    for i in range(1, 11):
        filename = f"初中语文九年级上册_页面_{i:03d}.jpg"
        if os.path.exists(filename):
            shutil.copy2(filename, os.path.join(media_folder, filename))
            copied_files.append(filename)
            print(f"✅ 已复制: {filename}")
    
    # 创建卡片模板
    cards = []
    for i, image_file in enumerate(copied_files, 1):
        # 基础填空题模板
        card_content = f'''<img src="{image_file}"><br><br>
根据图片内容，这是第{{{{c1::{i}}}}}页的内容。<br>
主要讲述了{{{{c2::请根据图片内容填写}}}}的知识点。'''
        
        cards.append([card_content, f"初中语文::九年级上册::页面{i}"])
    
    # 导出ANKI文件
    output_file = "anki_cards.txt"
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter='\t')
        for card in cards:
            writer.writerow(card)
    
    print(f"\n✅ 已创建 {len(cards)} 张卡片")
    print(f"📁 输出文件: {output_file}")
    print(f"🖼️  图片文件夹: {media_folder}")
    
    # 创建手动编辑模板
    create_manual_template(copied_files)
    
    print("\n📖 使用说明:")
    print("1. 编辑 manual_template.txt 文件，添加具体的问题和答案")
    print("2. 运行 python process_manual.py 生成最终卡片")
    print("3. 在ANKI中导入生成的文件")

def create_manual_template(image_files):
    """创建手动编辑模板"""
    with open("manual_template.txt", 'w', encoding='utf-8') as f:
        f.write("# 手动编辑ANKI卡片模板\n")
        f.write("# 格式: 图片文件名|问题|答案|标签\n\n")
        
        for i, image_file in enumerate(image_files, 1):
            f.write(f"{image_file}|这是关于什么的内容？|第{i}页的语文知识|初中语文,九年级上册\n")
    
    print("📝 已创建手动编辑模板: manual_template.txt")

if __name__ == "__main__":
    create_anki_cards()
