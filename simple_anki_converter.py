#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的图片转ANKI卡片工具 - 无需OCR
"""

import os
import csv
import shutil
from pathlib import Path

class SimpleAnkiConverter:
    def __init__(self, image_folder="."):
        self.image_folder = Path(image_folder)
        self.output_file = "anki_cards.txt"
        self.media_folder = "anki_media"
        
        # 创建媒体文件夹
        os.makedirs(self.media_folder, exist_ok=True)
        
    def copy_images_to_media(self, num_images=10):
        """复制前N张图片到媒体文件夹"""
        copied_files = []
        
        for i in range(1, num_images + 1):
            filename = f"初中语文九年级上册_页面_{i:03d}.jpg"
            source_path = self.image_folder / filename
            
            if source_path.exists():
                dest_path = Path(self.media_folder) / filename
                try:
                    shutil.copy2(source_path, dest_path)
                    copied_files.append(filename)
                    print(f"已复制: {filename}")
                except Exception as e:
                    print(f"复制 {filename} 失败: {e}")
        
        return copied_files
    
    def create_template_cards(self, image_files):
        """创建模板卡片"""
        cards = []
        
        for i, image_file in enumerate(image_files, 1):
            # 创建填空题模板
            card_content = f'''<img src="{image_file}"><br><br>
{{{{c1::关键词{i}}}}} 是本页的重要内容。<br>
请根据图片内容填写: {{{{c2::答案{i}}}}}'''
            
            cards.append({
                'content': card_content,
                'tags': '初中语文::九年级上册::页面' + str(i)
            })
        
        return cards
    
    def export_to_anki(self, cards):
        """导出为ANKI可导入的格式"""
        if not cards:
            print("没有卡片可导出")
            return
        
        with open(self.output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\t')
            
            for card in cards:
                writer.writerow([
                    card['content'],
                    card['tags']
                ])
        
        print(f"\n✅ 已创建 {len(cards)} 张模板卡片")
        print(f"📁 输出文件: {self.output_file}")
        print(f"🖼️  图片文件夹: {self.media_folder}")
        
    def generate_manual_template(self, image_files):
        """生成手动编辑的模板文件"""
        template_file = "manual_cards_template.txt"
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write("# ANKI填空题卡片模板\n")
            f.write("# 请根据图片内容修改下面的模板，然后运行 finalize_cards.py\n\n")
            
            for i, image_file in enumerate(image_files, 1):
                f.write(f"## 卡片 {i} - {image_file}\n")
                f.write(f"图片: {image_file}\n")
                f.write("问题: 请在这里写入基于图片内容的问题\n")
                f.write("答案: 请在这里写入答案（将成为填空内容）\n")
                f.write("标签: 初中语文,九年级上册,页面" + str(i) + "\n")
                f.write("-" * 50 + "\n\n")
        
        print(f"📝 已生成手动编辑模板: {template_file}")
        return template_file

def main():
    print("🚀 简单ANKI卡片转换工具")
    print("=" * 40)
    
    converter = SimpleAnkiConverter()
    
    # 复制图片文件
    print("📋 正在复制图片文件...")
    image_files = converter.copy_images_to_media(10)
    
    if not image_files:
        print("❌ 没有找到图片文件")
        return
    
    print(f"✅ 成功复制 {len(image_files)} 张图片")
    
    # 生成手动编辑模板
    template_file = converter.generate_manual_template(image_files)
    
    print("\n📋 下一步操作:")
    print(f"1. 打开 {template_file} 文件")
    print("2. 根据每张图片的内容，修改问题和答案")
    print("3. 运行 python finalize_cards.py 生成最终的ANKI文件")
    print("4. 在ANKI中导入生成的文件")
    
    # 询问是否直接创建简单模板
    print("\n🤔 或者，我可以直接创建简单的模板卡片供您参考")
    
    # 创建简单模板卡片
    cards = converter.create_template_cards(image_files)
    converter.export_to_anki(cards)
    
    print("\n📖 使用说明:")
    print("1. 在ANKI中选择 '文件' -> '导入'")
    print(f"2. 选择 {converter.output_file} 文件")
    print("3. 字段类型选择 'Cloze'")
    print(f"4. 将 {converter.media_folder} 文件夹中的图片复制到ANKI媒体文件夹")

if __name__ == "__main__":
    main()
