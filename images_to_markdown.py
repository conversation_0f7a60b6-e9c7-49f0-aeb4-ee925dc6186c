#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将前10张图片转换为Markdown文档
使用EasyOCR进行中文文字识别
"""

import os
import easyocr
from pathlib import Path
import re

class ImageToMarkdownConverter:
    def __init__(self, image_folder="."):
        self.image_folder = Path(image_folder)
        self.output_file = "语文九年级上册_前10页.md"
        
        # 初始化EasyOCR读取器，支持中文和英文
        print("正在初始化OCR引擎...")
        self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("OCR引擎初始化完成！")
        
    def extract_text_from_image(self, image_path):
        """使用EasyOCR从图片中提取文字"""
        try:
            print(f"正在识别图片: {image_path.name}")
            
            # 使用EasyOCR进行文字识别
            results = self.reader.readtext(str(image_path))
            
            # 提取文字内容
            text_lines = []
            for (bbox, text, confidence) in results:
                # 只保留置信度较高的文字
                if confidence > 0.3:
                    text_lines.append(text.strip())
            
            # 合并文字行
            full_text = '\n'.join(text_lines)
            
            # 清理文本
            full_text = self.clean_text(full_text)
            
            return full_text
            
        except Exception as e:
            print(f"处理图片 {image_path} 时出错: {e}")
            return ""
    
    def clean_text(self, text):
        """清理和格式化文本"""
        if not text.strip():
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n+', '\n', text)
        
        # 分割成行
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def process_images(self, num_images=10):
        """处理指定数量的图片"""
        # 获取前num_images张图片
        image_files = []
        for i in range(1, num_images + 1):
            filename = f"初中语文九年级上册_页面_{i:03d}.jpg"
            image_path = self.image_folder / filename
            if image_path.exists():
                image_files.append(image_path)
        
        if not image_files:
            print("没有找到图片文件")
            return
        
        print(f"找到 {len(image_files)} 张图片，开始处理...")
        
        # 创建Markdown内容
        markdown_content = []
        markdown_content.append("# 初中语文九年级上册 - 前10页内容")
        markdown_content.append("")
        markdown_content.append("*本文档由图片OCR自动生成*")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")
        
        for i, image_path in enumerate(image_files, 1):
            print(f"处理第 {i} 张图片: {image_path.name}")
            
            # 提取文字
            text = self.extract_text_from_image(image_path)
            
            # 添加到Markdown
            markdown_content.append(f"## 第 {i} 页")
            markdown_content.append("")
            
            if text:
                # 将文字按段落分割
                paragraphs = text.split('\n')
                for paragraph in paragraphs:
                    if paragraph.strip():
                        markdown_content.append(paragraph.strip())
                        markdown_content.append("")
                
                print(f"  ✅ 成功提取 {len(paragraphs)} 段文字")
            else:
                markdown_content.append("*此页面未能识别到文字内容*")
                markdown_content.append("")
                print(f"  ❌ 未能提取到文字")
            
            markdown_content.append("---")
            markdown_content.append("")
        
        return markdown_content
    
    def save_markdown(self, content):
        """保存Markdown文件"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            
            print(f"✅ Markdown文档已保存: {self.output_file}")
            
            # 显示文件信息
            file_size = os.path.getsize(self.output_file)
            print(f"📄 文件大小: {file_size} 字节")
            
        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")

def main():
    print("🚀 图片转Markdown工具")
    print("=" * 50)
    
    # 创建转换器实例
    converter = ImageToMarkdownConverter()
    
    # 处理图片
    markdown_content = converter.process_images(10)
    
    if markdown_content:
        # 保存Markdown文件
        converter.save_markdown(markdown_content)
        
        print("\n🎉 转换完成！")
        print(f"📖 请查看生成的文件: {converter.output_file}")
        
        # 显示使用说明
        print("\n📋 使用说明:")
        print("1. 生成的Markdown文件包含了前10页的文字内容")
        print("2. 可以使用任何Markdown编辑器打开查看")
        print("3. 如需编辑，建议使用支持中文的编辑器")
        
        print("\n🗑️  清理说明:")
        print("用完后可删除以下文件夹:")
        print("  - anki_env/ (虚拟环境)")
        
    else:
        print("❌ 没有成功处理任何图片")

if __name__ == "__main__":
    main()
