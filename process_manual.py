#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def process_manual_template():
    """处理手动编辑的模板"""
    cards = []
    
    try:
        with open("manual_template.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            if line.startswith('#') or not line.strip():
                continue
            
            parts = line.strip().split('|')
            if len(parts) >= 3:
                image_file, question, answer = parts[0], parts[1], parts[2]
                tags = parts[3] if len(parts) > 3 else "初中语文"
                
                # 创建填空题
                cloze_question = question.replace(answer, f"{{{{c1::{answer}}}}}")
                card_content = f'<img src="{image_file}"><br><br>{cloze_question}'
                
                cards.append([card_content, tags.replace(',', '::')])
        
        # 导出最终卡片
        with open("final_anki_cards.txt", 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\t')
            for card in cards:
                writer.writerow(card)
        
        print(f"✅ 已生成 {len(cards)} 张最终卡片: final_anki_cards.txt")
        
    except FileNotFoundError:
        print("❌ 找不到 manual_template.txt 文件")
    except Exception as e:
        print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    process_manual_template()
