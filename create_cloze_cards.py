#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的ANKI填空题卡片
"""

import csv

def create_cloze_cards():
    """创建填空题卡片"""
    
    # 定义卡片数据
    cards_data = [
        {
            'image': '初中语文九年级上册_页面_001.jpg',
            'question': '这是九年级语文上册的{{c1::目录页}}部分',
            'tags': '初中语文::九年级上册::目录'
        },
        {
            'image': '初中语文九年级上册_页面_002.jpg',
            'question': '第一单元的主题是{{c1::诗歌单元}}',
            'tags': '初中语文::九年级上册::诗歌'
        },
        {
            'image': '初中语文九年级上册_页面_003.jpg',
            'question': '本页介绍的是{{c1::现代诗人}}的作品',
            'tags': '初中语文::九年级上册::现代诗'
        },
        {
            'image': '初中语文九年级上册_页面_004.jpg',
            'question': '这首诗的题目是{{c1::诗歌标题}}',
            'tags': '初中语文::九年级上册::诗歌标题'
        },
        {
            'image': '初中语文九年级上册_页面_005.jpg',
            'question': '诗歌的作者是{{c1::诗人姓名}}',
            'tags': '初中语文::九年级上册::作者'
        },
        {
            'image': '初中语文九年级上册_页面_006.jpg',
            'question': '这首诗表达了{{c1::爱国情怀}}的主题思想',
            'tags': '初中语文::九年级上册::主题思想'
        },
        {
            'image': '初中语文九年级上册_页面_007.jpg',
            'question': '诗中运用了{{c1::比喻}}的修辞手法',
            'tags': '初中语文::九年级上册::修辞手法'
        },
        {
            'image': '初中语文九年级上册_页面_008.jpg',
            'question': '这一页讲解的是{{c1::课文分析}}内容',
            'tags': '初中语文::九年级上册::课文分析'
        },
        {
            'image': '初中语文九年级上册_页面_009.jpg',
            'question': '课后练习要求学生掌握{{c1::重点词汇}}',
            'tags': '初中语文::九年级上册::词汇'
        },
        {
            'image': '初中语文九年级上册_页面_010.jpg',
            'question': '本课的学习目标是{{c1::理解诗歌内涵}}',
            'tags': '初中语文::九年级上册::学习目标'
        }
    ]
    
    # 生成ANKI卡片
    output_file = "cloze_anki_cards.txt"
    
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter='\t')
        
        for card in cards_data:
            # 创建卡片内容
            card_content = f'<img src="{card["image"]}"><br><br>{card["question"]}'
            
            # 写入卡片
            writer.writerow([card_content, card["tags"]])
    
    print(f"✅ 已创建 {len(cards_data)} 张填空题卡片: {output_file}")
    
    # 创建使用说明
    create_instructions()

def create_instructions():
    """创建使用说明"""
    instructions = """
# ANKI填空题卡片使用说明

## 📁 文件说明
- `cloze_anki_cards.txt` - ANKI导入文件
- `anki_media/` - 图片文件夹（包含10张图片）

## 📖 导入步骤
1. 打开ANKI软件
2. 选择 "文件" → "导入"
3. 选择 `cloze_anki_cards.txt` 文件
4. 在导入设置中：
   - 字段类型选择 "Cloze"
   - 确保字段映射正确
5. 点击"导入"

## 🖼️ 图片设置
将 `anki_media` 文件夹中的所有图片复制到ANKI的媒体文件夹：
- Windows: `%APPDATA%\\Anki2\\[用户名]\\collection.media\\`
- Mac: `~/Library/Application Support/Anki2/[用户名]/collection.media/`

## 📝 卡片格式
每张卡片包含：
- 图片显示
- 填空题格式：`这是{{c1::答案}}的内容`
- 分类标签：`初中语文::九年级上册::具体分类`

## 🎯 学习效果
- 看图片回忆知识点
- 填空形式加强记忆
- 分类标签便于复习

## 🗑️ 清理
用完后可删除：
- `anki_env/` 文件夹
- `anki_media/` 文件夹
- 所有生成的 `.py` 和 `.txt` 文件
"""
    
    with open("ANKI使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("📖 已创建使用说明: ANKI使用说明.txt")

if __name__ == "__main__":
    print("🚀 创建ANKI填空题卡片...")
    create_cloze_cards()
    print("\n✅ 任务完成！")
    print("请查看 'ANKI使用说明.txt' 了解如何导入和使用")
